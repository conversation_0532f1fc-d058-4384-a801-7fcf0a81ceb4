import { supabase, Metric } from './supabase'

export interface MetricsData {
  appointments: any[]
  totalAppointments: number
  totalSits: number
  totalCloses: number
  noShows: number
  rescheduled: number
  notInterested: number
  disqualified: number
}

export const metricsService = {
  // Fetch metrics for a specific company
  async getCompanyMetrics(companyId: string): Promise<Metric[]> {
    const { data, error } = await supabase
      .from('metrics')
      .select('*')
      .eq('company_id', companyId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching company metrics:', error)
      throw error
    }

    return data || []
  },

  // Fetch all metrics (for super admins)
  async getAllMetrics(): Promise<Metric[]> {
    const { data, error } = await supabase
      .from('metrics')
      .select(`
        *,
        company:companies(*)
      `)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching all metrics:', error)
      throw error
    }

    return data || []
  },

  // Create new metric entry
  async createMetric(companyId: string, metricType: string, metricData: any): Promise<Metric> {
    const { data, error } = await supabase
      .from('metrics')
      .insert({
        company_id: companyId,
        metric_type: metricType,
        metric_data: metricData
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating metric:', error)
      throw error
    }

    return data
  },

  // Update existing metric
  async updateMetric(metricId: string, metricData: any): Promise<Metric> {
    const { data, error } = await supabase
      .from('metrics')
      .update({
        metric_data: metricData,
        updated_at: new Date().toISOString()
      })
      .eq('id', metricId)
      .select()
      .single()

    if (error) {
      console.error('Error updating metric:', error)
      throw error
    }

    return data
  },

  // Delete metric
  async deleteMetric(metricId: string): Promise<void> {
    const { error } = await supabase
      .from('metrics')
      .delete()
      .eq('id', metricId)

    if (error) {
      console.error('Error deleting metric:', error)
      throw error
    }
  },

  // Convert mock appointment data to metrics format
  convertAppointmentsToMetrics(appointments: any[]): MetricsData {
    const totalAppointments = appointments.length
    const totalSits = appointments.filter(apt => apt.confirmation_disposition === 'Sat').length
    const totalCloses = appointments.filter(apt => apt.confirmation_disposition === 'Closed').length
    const noShows = appointments.filter(apt => apt.confirmation_disposition === 'No Show').length
    const rescheduled = appointments.filter(apt => apt.confirmation_disposition === 'Rescheduled').length
    const notInterested = appointments.filter(apt => apt.confirmation_disposition === 'Not Interested').length
    const disqualified = appointments.filter(apt => apt.confirmation_disposition === 'Disqualified').length

    return {
      appointments,
      totalAppointments,
      totalSits,
      totalCloses,
      noShows,
      rescheduled,
      notInterested,
      disqualified
    }
  }
}
