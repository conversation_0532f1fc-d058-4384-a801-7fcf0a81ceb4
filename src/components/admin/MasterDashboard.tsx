import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { MetricCard } from '@/components/ui/metric-card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { supabase, Company, User, Metric } from '@/lib/supabase'
import { BarChart3, Building2, Users, TrendingUp, Activity } from 'lucide-react'

interface CompanyStats {
  company: Company
  userCount: number
  metricsCount: number
  lastActivity: string | null
}

export const MasterDashboard = () => {
  const [companies, setCompanies] = useState<Company[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [metrics, setMetrics] = useState<Metric[]>([])
  const [companyStats, setCompanyStats] = useState<CompanyStats[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      // Fetch all data in parallel
      const [companiesResult, usersResult, metricsResult] = await Promise.all([
        supabase.from('companies').select('*').order('created_at', { ascending: false }),
        supabase.from('users').select('*'),
        supabase.from('metrics').select('*').order('created_at', { ascending: false })
      ])

      if (companiesResult.error) throw companiesResult.error
      if (usersResult.error) throw usersResult.error
      if (metricsResult.error) throw metricsResult.error

      const companiesData = companiesResult.data || []
      const usersData = usersResult.data || []
      const metricsData = metricsResult.data || []

      setCompanies(companiesData)
      setUsers(usersData)
      setMetrics(metricsData)

      // Calculate company stats
      const stats: CompanyStats[] = companiesData.map(company => {
        const companyUsers = usersData.filter(user => user.company_id === company.id)
        const companyMetrics = metricsData.filter(metric => metric.company_id === company.id)
        const lastMetric = companyMetrics[0] // Already sorted by created_at desc

        return {
          company,
          userCount: companyUsers.length,
          metricsCount: companyMetrics.length,
          lastActivity: lastMetric?.created_at || null
        }
      })

      setCompanyStats(stats)
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const totalUsers = users.length
  const totalSuperAdmins = users.filter(user => user.role === 'super_admin').length
  const totalRegularUsers = users.filter(user => user.role === 'user').length
  const activeUsers = users.filter(user => user.is_active).length

  if (loading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="text-center py-12">
            <div className="text-lg">Loading master dashboard...</div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total Companies"
          value={companies.length.toString()}
          icon={Building2}
          trend={companies.length > 0 ? 'up' : 'neutral'}
          trendValue=""
        />
        <MetricCard
          title="Total Users"
          value={totalUsers.toString()}
          icon={Users}
          trend={activeUsers === totalUsers ? 'up' : 'down'}
          trendValue={`${activeUsers} active`}
        />
        <MetricCard
          title="Super Admins"
          value={totalSuperAdmins.toString()}
          icon={Activity}
          trend="neutral"
          trendValue=""
        />
        <MetricCard
          title="Total Metrics"
          value={metrics.length.toString()}
          icon={TrendingUp}
          trend={metrics.length > 0 ? 'up' : 'neutral'}
          trendValue=""
        />
      </div>

      {/* Companies Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Companies Overview
          </CardTitle>
          <CardDescription>
            Detailed view of all companies and their activity
          </CardDescription>
        </CardHeader>
        <CardContent>
          {companyStats.length === 0 ? (
            <Alert>
              <AlertDescription>
                No companies have been created yet. Create your first company to get started.
              </AlertDescription>
            </Alert>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Company</TableHead>
                  <TableHead>Company ID</TableHead>
                  <TableHead>Users</TableHead>
                  <TableHead>Metrics</TableHead>
                  <TableHead>Last Activity</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {companyStats.map((stat) => (
                  <TableRow key={stat.company.id}>
                    <TableCell className="font-medium">{stat.company.name}</TableCell>
                    <TableCell className="font-mono text-sm">{stat.company.company_id}</TableCell>
                    <TableCell>
                      <Badge variant="secondary">
                        {stat.userCount} user{stat.userCount !== 1 ? 's' : ''}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={stat.metricsCount > 0 ? 'default' : 'outline'}>
                        {stat.metricsCount} metric{stat.metricsCount !== 1 ? 's' : ''}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {stat.lastActivity 
                        ? new Date(stat.lastActivity).toLocaleDateString()
                        : 'No activity'
                      }
                    </TableCell>
                    <TableCell>
                      <Badge variant={stat.userCount > 0 ? 'default' : 'secondary'}>
                        {stat.userCount > 0 ? 'Active' : 'Setup Needed'}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Recent Activity
          </CardTitle>
          <CardDescription>
            Latest metrics and system activity
          </CardDescription>
        </CardHeader>
        <CardContent>
          {metrics.length === 0 ? (
            <Alert>
              <AlertDescription>
                No metrics data available yet. Metrics will appear here once companies start using the system.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-4">
              {metrics.slice(0, 10).map((metric) => {
                const company = companies.find(c => c.id === metric.company_id)
                return (
                  <div key={metric.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <p className="font-medium">{metric.metric_type}</p>
                      <p className="text-sm text-muted-foreground">
                        {company?.name || 'Unknown Company'} • {new Date(metric.created_at).toLocaleString()}
                      </p>
                    </div>
                    <Badge variant="outline">
                      {new Date(metric.date_recorded).toLocaleDateString()}
                    </Badge>
                  </div>
                )
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* System Health */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            System Health
          </CardTitle>
          <CardDescription>
            Overall system status and health metrics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-green-600">{activeUsers}</div>
              <div className="text-sm text-muted-foreground">Active Users</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{companies.length}</div>
              <div className="text-sm text-muted-foreground">Active Companies</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{metrics.length}</div>
              <div className="text-sm text-muted-foreground">Total Metrics</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
