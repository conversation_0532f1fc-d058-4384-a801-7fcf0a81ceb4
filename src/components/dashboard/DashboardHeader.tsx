import { Building2, LogOut, User } from "lucide-react";
import { MetricsVisibilityPanel } from "./MetricsVisibilityPanel";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";

interface DashboardHeaderProps {
  clientName?: string;
  logoUrl?: string;
  visibleMetrics?: Record<string, boolean>;
  onToggleMetric?: (metric: string) => void;
}

export function DashboardHeader({
  clientName = "{{client_name}}",
  logoUrl = "[INSERT_LOGO_URL_HERE]",
  visibleMetrics,
  onToggleMetric
}: DashboardHeaderProps) {
  const { userProfile, signOut, isSuperAdmin } = useAuth();

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };
  return (
    <header className="w-full bg-card border-b border-border px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <img
            src="/lovable-uploads/58030552-ef5b-484d-bb0e-66450b796c1d.png"
            alt="Clients Dashboard"
            className="h-12 w-12 rounded-lg"
            onError={(e) => {
              // Fallback to icon if image fails to load
              const fallback = document.createElement('div');
              fallback.className = 'h-12 w-12 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center';
              fallback.innerHTML = '<svg class="h-8 w-8 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/></svg>';
              (e.target as HTMLImageElement).parentNode?.replaceChild(fallback, e.target as HTMLImageElement);
            }}
          />
          <div>
            <h1 className="text-xl font-bold text-foreground">
              {isSuperAdmin ? 'Admin Dashboard' : 'Clients Dashboard'}
            </h1>
            <p className="text-sm text-muted-foreground">
              {isSuperAdmin ? 'Master Analytics View' : 'Company Analytics'}
            </p>
          </div>
        </div>

        <div className="flex items-center">
          <div className="text-right">
            <p className="text-lg font-bold text-primary">{clientName}</p>
            {userProfile?.company_id && !isSuperAdmin && (
              <p className="text-xs text-muted-foreground">Company View</p>
            )}
            {isSuperAdmin && (
              <p className="text-xs text-muted-foreground">All Companies</p>
            )}
          </div>
        </div>

        <div className="flex items-center gap-4">
          {visibleMetrics && onToggleMetric && (
            <MetricsVisibilityPanel
              visibleMetrics={visibleMetrics}
              onToggleMetric={onToggleMetric}
            />
          )}

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span className="hidden sm:inline">
                  {userProfile?.first_name || userProfile?.email || 'User'}
                </span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium">
                    {userProfile?.first_name && userProfile?.last_name
                      ? `${userProfile.first_name} ${userProfile.last_name}`
                      : userProfile?.email
                    }
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {isSuperAdmin ? 'Super Admin' : 'User'}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              {isSuperAdmin && (
                <DropdownMenuItem asChild>
                  <a href="/admin">Admin Portal</a>
                </DropdownMenuItem>
              )}
              <DropdownMenuItem onClick={handleSignOut}>
                <LogOut className="mr-2 h-4 w-4" />
                Sign Out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}