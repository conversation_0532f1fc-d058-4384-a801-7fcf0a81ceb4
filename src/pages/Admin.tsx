import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { CompaniesManagement } from '@/components/admin/CompaniesManagement'
import { UsersManagement } from '@/components/admin/UsersManagement'
import { AdminHeader } from '@/components/admin/AdminHeader'
import { MasterDashboard } from '@/components/admin/MasterDashboard'
import { Building2, Users, BarChart3 } from 'lucide-react'

const Admin = () => {
  const [activeTab, setActiveTab] = useState('companies')

  return (
    <div className="min-h-screen bg-dashboard-bg">
      <AdminHeader />
      
      <main className="container mx-auto px-6 py-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-foreground mb-2">Admin Portal</h1>
          <p className="text-muted-foreground">
            Manage companies, users, and system settings
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="companies" className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              Companies
            </TabsTrigger>
            <TabsTrigger value="users" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Users
            </TabsTrigger>
            <TabsTrigger value="dashboard" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Master Dashboard
            </TabsTrigger>
          </TabsList>

          <TabsContent value="companies" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Companies Management</CardTitle>
                <CardDescription>
                  Create, edit, and manage client companies
                </CardDescription>
              </CardHeader>
              <CardContent>
                <CompaniesManagement />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="users" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Users Management</CardTitle>
                <CardDescription>
                  Create, edit, and manage user accounts
                </CardDescription>
              </CardHeader>
              <CardContent>
                <UsersManagement />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="dashboard" className="space-y-6">
            <MasterDashboard />
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}

export default Admin
