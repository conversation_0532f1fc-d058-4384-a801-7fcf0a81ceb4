import { useState, useMemo, useEffect } from "react";
import { Users, Target, Calendar, TrendingUp, UserCheck, UserX, RotateCcw, XCircle, CheckCircle } from "lucide-react";
import { isWithinInterval, parseISO, startOfWeek, endOfWeek, subDays } from "date-fns";

// Components
import { DashboardHeader } from "@/components/dashboard/DashboardHeader";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { AppointmentsTable } from "@/components/dashboard/AppointmentsTable";
import { AppointmentBreakdown } from "@/components/dashboard/AppointmentBreakdown";
import { TimeSeriesChart } from "@/components/dashboard/TimeSeriesChart";
import { MetricCard } from "@/components/ui/metric-card";
import { Alert, AlertDescription } from "@/components/ui/alert";

// Data and Auth
import { mockAppointments, Appointment } from "@/data/mockData";
import { useAuth } from "@/contexts/AuthContext";
import { supabase, Metric } from "@/lib/supabase";

const Index = () => {
  const { userProfile, isSuperAdmin } = useAuth();
  const today = new Date();
  const [filters, setFilters] = useState({
    dateRange: {
      start: startOfWeek(today, { weekStartsOn: 1 }),
      end: endOfWeek(today, { weekStartsOn: 1 })
    },
    selectedClosers: [] as string[],
    selectedSetters: [] as string[]
  });

  const [metrics, setMetrics] = useState<Metric[]>([]);
  const [loading, setLoading] = useState(true);

  const [visibleMetrics, setVisibleMetrics] = useState({
    totalAppointments: true,
    totalSits: true,
    totalCloses: true,
    noShows: true,
    rescheduled: true,
    notInterested: true,
    disqualified: true,
    appointmentBreakdown: true,
    detailedTable: true,
    performanceChart: true
  });

  // Fetch metrics data based on user role and company
  useEffect(() => {
    const fetchMetrics = async () => {
      if (!userProfile) return;

      try {
        let query = supabase.from('metrics').select('*');

        // If user is not super admin, filter by their company
        if (!isSuperAdmin && userProfile.company_id) {
          query = query.eq('company_id', userProfile.company_id);
        }

        const { data, error } = await query.order('created_at', { ascending: false });

        if (error) throw error;
        setMetrics(data || []);
      } catch (error) {
        console.error('Error fetching metrics:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchMetrics();
  }, [userProfile, isSuperAdmin]);

  // Extract unique closers and setters
  const closers = useMemo(() => 
    [...new Set(mockAppointments.map(apt => apt.closer_name))].sort(),
    []
  );
  
  const setters = useMemo(() => 
    [...new Set(mockAppointments.map(apt => apt.setter_name))].sort(),
    []
  );

  // Filter appointments based on current filters
  const filteredAppointments = useMemo(() => {
    return mockAppointments.filter(appointment => {
      // Date range filter
      const appointmentDate = parseISO(appointment.booked_for);
      const isInDateRange = isWithinInterval(appointmentDate, {
        start: filters.dateRange.start,
        end: filters.dateRange.end
      });
      
      // Closer filter
      const closerMatch = filters.selectedClosers.length === 0 || 
        filters.selectedClosers.includes(appointment.closer_name);
      
      // Setter filter
      const setterMatch = filters.selectedSetters.length === 0 || 
        filters.selectedSetters.includes(appointment.setter_name);
      
      return isInDateRange && closerMatch && setterMatch;
    });
  }, [filters]);

  // Calculate KPIs with previous period comparison
  const calculatedMetrics = useMemo(() => {
    const total = filteredAppointments.length;
    const sits = filteredAppointments.filter(apt => apt.confirmation_disposition === 'Sat').length;
    const closes = filteredAppointments.filter(apt => apt.confirmation_disposition === 'Closed').length;
    const noShows = filteredAppointments.filter(apt => apt.confirmation_disposition === 'No Show').length;
    const rescheduled = filteredAppointments.filter(apt => apt.confirmation_disposition === 'Rescheduled').length;
    const notInterested = filteredAppointments.filter(apt => apt.confirmation_disposition === 'Not Interested').length;
    const disqualified = filteredAppointments.filter(apt => apt.confirmation_disposition === 'Disqualified').length;
    
    // Calculate percentage change vs previous period
    const currentPeriodDays = Math.ceil((filters.dateRange.end.getTime() - filters.dateRange.start.getTime()) / (1000 * 60 * 60 * 24));
    const previousStart = subDays(filters.dateRange.start, currentPeriodDays);
    const previousEnd = subDays(filters.dateRange.end, currentPeriodDays);
    
    const previousAppointments = mockAppointments.filter(appointment => {
      const appointmentDate = parseISO(appointment.booked_for);
      return isWithinInterval(appointmentDate, {
        start: previousStart,
        end: previousEnd
      });
    });
    
    const previousTotal = previousAppointments.length;
    const percentageChange = previousTotal > 0 ? (((total - previousTotal) / previousTotal) * 100) : 0;
    
    return {
      total,
      sits,
      closes,
      noShows,
      rescheduled,
      notInterested,
      disqualified,
      percentageChange: percentageChange.toFixed(1),
      percentageChangeNumeric: percentageChange,
      noShowsPercentage: total > 0 ? ((noShows / total) * 100).toFixed(1) : '0.0',
      rescheduledPercentage: total > 0 ? ((rescheduled / total) * 100).toFixed(1) : '0.0',
      notInterestedPercentage: total > 0 ? ((notInterested / total) * 100).toFixed(1) : '0.0',
      disqualifiedPercentage: total > 0 ? ((disqualified / total) * 100).toFixed(1) : '0.0',
    };
  }, [filteredAppointments, filters.dateRange]);

  const toggleMetric = (metric: string) => {
    setVisibleMetrics(prev => ({
      ...prev,
      [metric]: !prev[metric]
    }));
  };

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-dashboard-bg">
        <DashboardHeader
          visibleMetrics={visibleMetrics}
          onToggleMetric={toggleMetric}
        />
        <main className="container mx-auto px-6 py-6">
          <div className="text-center py-12">
            <div className="text-lg">Loading dashboard...</div>
          </div>
        </main>
      </div>
    );
  }

  // Show message if no company assigned (for regular users)
  if (!isSuperAdmin && !userProfile?.company_id) {
    return (
      <div className="min-h-screen bg-dashboard-bg">
        <DashboardHeader
          visibleMetrics={visibleMetrics}
          onToggleMetric={toggleMetric}
        />
        <main className="container mx-auto px-6 py-6">
          <Alert>
            <AlertDescription>
              You are not assigned to a company yet. Please contact your administrator.
            </AlertDescription>
          </Alert>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-dashboard-bg">
      <DashboardHeader
        visibleMetrics={visibleMetrics}
        onToggleMetric={toggleMetric}
        clientName={
          isSuperAdmin
            ? "Master Dashboard"
            : userProfile?.company_id
              ? `Company Dashboard`
              : "Dashboard"
        }
      />

      <main className="container mx-auto px-6 py-6">
        {/* Show company info for regular users */}
        {!isSuperAdmin && userProfile?.company_id && (
          <div className="mb-6">
            <Alert>
              <AlertDescription>
                Viewing data for your company. {metrics.length === 0 && "No metrics data available yet."}
              </AlertDescription>
            </Alert>
          </div>
        )}

        {/* Show admin notice for super admins */}
        {isSuperAdmin && (
          <div className="mb-6">
            <Alert>
              <AlertDescription>
                Super Admin View: {metrics.length === 0 ? "No metrics data from any company yet." : `Viewing aggregated data from all companies (${metrics.length} metrics).`}
              </AlertDescription>
            </Alert>
          </div>
        )}

        {/* Filters */}
        <FilterBar
          closers={closers}
          setters={setters}
          onFiltersChange={setFilters}
        />

        {/* Performance Over Time Chart */}
        {visibleMetrics.performanceChart && (
          <TimeSeriesChart appointments={filteredAppointments} dateRange={filters.dateRange} />
        )}

        {/* KPI Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {visibleMetrics.totalAppointments && (
            <MetricCard
              title="Total Appointments"
              value={calculatedMetrics.total}
              subtitle={
                <span>
                  All appointments in selected period<br />
                  <span className="text-blue-600 font-semibold">
                    Change: {calculatedMetrics.percentageChangeNumeric > 0 ? '+' : ''}{calculatedMetrics.percentageChange}% vs previous period
                  </span>
                </span>
              }
              icon={Calendar}
              variant="info"
            />
          )}
          {visibleMetrics.totalSits && (
            <MetricCard
              title="Total Sits"
              value={calculatedMetrics.sits}
              subtitle={
                <span>
                  Successfully sat appointments<br />
                  <span className="text-green-600 font-semibold">
                    Show Rate: {calculatedMetrics.total > 0 ? ((calculatedMetrics.sits / calculatedMetrics.total) * 100).toFixed(1) : '0.0'}%
                  </span>
                </span>
              }
              icon={UserCheck}
              variant="success"
            />
          )}
          {visibleMetrics.totalCloses && (
            <MetricCard
              title="Total Closes"
              value={calculatedMetrics.closes}
              subtitle={
                <span>
                  Successfully closed deals<br />
                  <span className="text-green-600 font-semibold">
                    Close Rate: {calculatedMetrics.total > 0 ? ((calculatedMetrics.closes / calculatedMetrics.total) * 100).toFixed(1) : '0.0'}%
                  </span>
                </span>
              }
              icon={CheckCircle}
              variant="success"
            />
          )}
        </div>

        {/* Disposition Analysis Section */}
        {(visibleMetrics.noShows || visibleMetrics.rescheduled || visibleMetrics.notInterested || visibleMetrics.disqualified) && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-foreground mb-4">Disposition Analysis</h2>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {visibleMetrics.noShows && (
                <MetricCard
                  title="No Show"
                  value={calculatedMetrics.noShows}
                  subtitle={`${calculatedMetrics.noShowsPercentage}% of total appointments`}
                  icon={XCircle}
                  variant="default"
                />
              )}
              {visibleMetrics.rescheduled && (
                <MetricCard
                  title="Rescheduled"
                  value={calculatedMetrics.rescheduled}
                  subtitle={`${calculatedMetrics.rescheduledPercentage}% of total appointments`}
                  icon={RotateCcw}
                  variant="warning"
                />
              )}
              {visibleMetrics.notInterested && (
                <MetricCard
                  title="Not Interested"
                  value={calculatedMetrics.notInterested}
                  subtitle={`${calculatedMetrics.notInterestedPercentage}% of total appointments`}
                  icon={UserX}
                  variant="default"
                />
              )}
              {visibleMetrics.disqualified && (
                <MetricCard
                  title="Disqualified"
                  value={calculatedMetrics.disqualified}
                  subtitle={`${calculatedMetrics.disqualifiedPercentage}% of total appointments`}
                  icon={Target}
                  variant="default"
                />
              )}
            </div>
          </div>
        )}

        {/* Appointment Breakdown Section */}
        {visibleMetrics.appointmentBreakdown && (
          <AppointmentBreakdown appointments={filteredAppointments} />
        )}

        {/* Detailed Appointments Table */}
        {visibleMetrics.detailedTable && (
          <AppointmentsTable appointments={filteredAppointments} />
        )}
      </main>
    </div>
  );
};

export default Index;
