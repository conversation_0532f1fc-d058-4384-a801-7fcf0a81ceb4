// <PERSON>ript to create initial super admin user
// Run this once to set up the first super admin

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.VITE_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function createSuperAdmin() {
  try {
    // Create auth user
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'Admin123!',
      email_confirm: true
    })

    if (authError) {
      console.error('Error creating auth user:', authError)
      return
    }

    console.log('Auth user created:', authData.user.id)

    // Update user profile to super admin
    const { error: profileError } = await supabaseAdmin
      .from('users')
      .update({
        role: 'super_admin',
        first_name: 'Super',
        last_name: 'Admin'
      })
      .eq('id', authData.user.id)

    if (profileError) {
      console.error('Error updating user profile:', profileError)
      return
    }

    console.log('Super admin user created successfully!')
    console.log('Email: <EMAIL>')
    console.log('Password: Admin123!')
    console.log('Please change the password after first login.')

  } catch (error) {
    console.error('Error:', error)
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  createSuperAdmin()
}

export { createSuperAdmin }
